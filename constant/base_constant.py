# 声明：本代码仅供学习和研究目的使用。使用者应遵守以下原则：  
# 1. 不得用于任何商业用途。  
# 2. 使用时应遵守目标平台的使用条款和robots.txt规则。  
# 3. 不得进行大规模爬取或对平台造成运营干扰。  
# 4. 应合理控制请求频率，避免给目标平台带来不必要的负担。   
# 5. 不得用于任何非法或不当的用途。
#   
# 详细许可条款请参阅项目根目录下的LICENSE文件。  
# 使用本代码即表示您同意遵守上述原则和LICENSE中的所有条款。  


# -*- coding: utf-8 -*-
# request id
import config

REQUEST_ID_HEADERS_KEY = "x-request-id"
RESPONSE_TYPE_JSON_HEADERS_KEY = "x-response-type"
RESPONSE_TYPE_JSON_HEADERS_VALUE = "json"

JAVASCRIPT_SIGN_SERVER = "javascript"
PLAYWRIGHT_SIGN_SERVER = "playwright"
PYTHON_SIGN_SERVER = "python"

XHS_PLATFORM_NAME = "xiaohongshu"
XHS_INDEX_URL = "https://www.xiaohongshu.com/"
XHS_PLAYWRIGHT_SIGN = PLAYWRIGHT_SIGN_SERVER
XHS_JAVASCRIPT_SIGN = JAVASCRIPT_SIGN_SERVER
XHS_DEFAULT_SIGN_SERVER = PLAYWRIGHT_SIGN_SERVER if config.sign_type == PLAYWRIGHT_SIGN_SERVER else XHS_JAVASCRIPT_SIGN

DOUYIN_PLATFORM_NAME = "douyin"
DOUYIN_INDEX_URL = "https://www.douyin.com/"
DOUYIN_PLAYWRIGHT_SIGN = PLAYWRIGHT_SIGN_SERVER
DOUYIN_JAVASCRIPT_SIGN = JAVASCRIPT_SIGN_SERVER
DOUYIN_DEFAULT_SIGN_SERVER = PLAYWRIGHT_SIGN_SERVER if config.sign_type == PLAYWRIGHT_SIGN_SERVER else DOUYIN_JAVASCRIPT_SIGN


BILIBILI_PLATFORM_NAME = "bilibili"
BILIBILI_INDEX_URL = "https://www.bilibili.com/"
BILIBILI_API_URL = "https://api.bilibili.com"
BILIBILI_PLAYWRIGHT_SIGN = PLAYWRIGHT_SIGN_SERVER
BILIBILI_PYTHON_SIGN = PYTHON_SIGN_SERVER
BILIBILI_DEFAULT_SIGN_SERVER = PLAYWRIGHT_SIGN_SERVER if config.sign_type == PLAYWRIGHT_SIGN_SERVER else BILIBILI_PYTHON_SIGN


ZHIHU_PLATFORM_NAME = "zhihu"
ZHIHU_JAVASCRIPT_SIGN = JAVASCRIPT_SIGN_SERVER
ZHIHU_DEFAULT_SIGN_SERVER = ZHIHU_JAVASCRIPT_SIGN

ONE_SECOND = 1
ONE_MINUTE = 60 * ONE_SECOND
ONE_HOUR = 60 * ONE_MINUTE
PLAYWRIGHT_INDEX_LOAD_TIME = 2 * ONE_SECOND
